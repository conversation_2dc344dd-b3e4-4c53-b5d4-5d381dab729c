import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/account_controller.dart';
import 'login_view.dart';
import 'register_view.dart';

class AccountView extends GetView<AccountController> {
  const AccountView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => controller.isLoginPage.value 
        ? const LoginView()
        : const RegisterView(),
    );
  }
}
