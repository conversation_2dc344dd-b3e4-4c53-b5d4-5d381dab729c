import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'account_controller.dart';

class RegisterController extends GetxController {
  // Text editing controllers
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  // Observable variables
  var isPasswordVisible = false.obs;
  var agreeToTerms = false.obs;
  var isLoading = false.obs;

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  // Methods
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  void toggleAgreeToTerms(bool? value) {
    agreeToTerms.value = value ?? false;
  }

  void onTermsAndConditionsTap() {
    showFeedback("Terms & Conditions clicked!");
  }

  void onSignInTap() {
    // Navigate to login page
    final accountController = Get.find<AccountController>();
    accountController.showLoginPage();
    showFeedback("Switched to Login!");
  }

  Future<void> handleRegister() async {
    if (nameController.text.isEmpty ||
        emailController.text.isEmpty ||
        passwordController.text.isEmpty) {
      showFeedback("Please fill all fields!");
      return;
    }

    if (!agreeToTerms.value) {
      showFeedback("Please agree to terms & conditions!");
      return;
    }

    isLoading.value = true;

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    isLoading.value = false;

    showFeedback("Account created successfully! Welcome to Ar-Rahnu Auction!");
  }

  void showFeedback(String message) {
    Get.snackbar(
      "Ar-Rahnu Auction",
      message,
      backgroundColor: const Color(0xFFFE8000),
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }
}
