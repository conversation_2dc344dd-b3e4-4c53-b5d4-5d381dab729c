import 'package:get/get.dart';

enum AuctionStatus { beforeStart, active, ended }

class HomeController extends GetxController {
  // Observable variables
  var selectedCategory = 'All'.obs;
  var searchQuery = ''.obs;
  var isLoading = false.obs;

  // Auction session state
  var auctionStatus = AuctionStatus.beforeStart.obs;
  var timeRemaining = '0d 11h 44m 30s'.obs;

  // Categories list
  final categories = [
    'All',
    'Gold',
    'Silver',
    'Jewelry',
    'Electronics',
    'Watches',
  ];

  // Sample auction items
  final auctionItems = [
    {
      'title': 'Gold Necklace',
      'currentBid': 'RM 2,500',
      'timeLeft': '2h 30m',
      'image': 'assets/gold_necklace.jpg',
      'category': 'Gold',
    },
    {
      'title': 'Diamond Ring',
      'currentBid': 'RM 5,200',
      'timeLeft': '1h 15m',
      'image': 'assets/diamond_ring.jpg',
      'category': 'Jewelry',
    },
    {
      'title': 'Silver Watch',
      'currentBid': 'RM 850',
      'timeLeft': '4h 45m',
      'image': 'assets/silver_watch.jpg',
      'category': 'Watches',
    },
    {
      'title': 'Gold Bracelet',
      'currentBid': 'RM 1,800',
      'timeLeft': '3h 20m',
      'image': 'assets/gold_bracelet.jpg',
      'category': 'Gold',
    },
  ];

  // Methods
  void selectCategory(String category) {
    selectedCategory.value = category;
  }

  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  void onSearchTap() {
    // Handle search functionality
    // TODO: Implement search functionality
  }

  void onViewAllTap() {
    // Navigate to view all auctions
    // TODO: Implement view all navigation
  }

  void onAuctionItemTap(Map<String, dynamic> item) {
    // Navigate to auction item details
    // TODO: Implement auction item navigation
  }

  void onCalendarTap() {
    // Show calendar view
    // TODO: Implement calendar functionality
  }

  void onJoinLiveAuctionTap() {
    // Join live auction
    // TODO: Implement live auction functionality
  }

  // Auction status management
  void simulateAuctionStart() {
    auctionStatus.value = AuctionStatus.active;
    timeRemaining.value = '3d 12h 30m 15s'; // Time until auction ends
  }

  void simulateAuctionEnd() {
    auctionStatus.value = AuctionStatus.ended;
    timeRemaining.value = '0d 0h 0m 0s';
  }

  void resetToBeforeStart() {
    auctionStatus.value = AuctionStatus.beforeStart;
    timeRemaining.value = '0d 11h 44m 30s';
  }

  // Check if auction items should be displayed
  bool get shouldShowAuctionItems =>
      auctionStatus.value == AuctionStatus.active;

  // Check if session info should be displayed
  bool get shouldShowSessionInfo =>
      auctionStatus.value == AuctionStatus.beforeStart ||
      auctionStatus.value == AuctionStatus.ended;

  // Filtered items based on selected category
  List<Map<String, dynamic>> get filteredItems {
    if (selectedCategory.value == 'All') {
      return auctionItems;
    }
    return auctionItems
        .where((item) => item['category'] == selectedCategory.value)
        .toList();
  }
}
