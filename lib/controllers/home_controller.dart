import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomeController extends GetxController {
  // Observable variables
  var selectedCategory = 'All'.obs;
  var searchQuery = ''.obs;
  var isLoading = false.obs;

  // Categories list
  final categories = [
    'All',
    'Gold',
    'Silver',
    'Jewelry',
    'Electronics',
    'Watches',
  ];

  // Sample auction items
  final auctionItems = [
    {
      'title': 'Gold Necklace',
      'currentBid': 'RM 2,500',
      'timeLeft': '2h 30m',
      'image': 'assets/gold_necklace.jpg',
      'category': 'Gold',
    },
    {
      'title': 'Diamond Ring',
      'currentBid': 'RM 5,200',
      'timeLeft': '1h 15m',
      'image': 'assets/diamond_ring.jpg',
      'category': 'Jewelry',
    },
    {
      'title': 'Silver Watch',
      'currentBid': 'RM 850',
      'timeLeft': '4h 45m',
      'image': 'assets/silver_watch.jpg',
      'category': 'Watches',
    },
    {
      'title': 'Gold Bracelet',
      'currentBid': 'RM 1,800',
      'timeLeft': '3h 20m',
      'image': 'assets/gold_bracelet.jpg',
      'category': 'Gold',
    },
  ];

  // Methods
  void selectCategory(String category) {
    selectedCategory.value = category;
    showFeedback("$category selected!");
  }

  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  void onSearchTap() {
    showFeedback("Search functionality clicked!");
  }

  void onViewAllTap() {
    showFeedback("View All clicked!");
  }

  void onAuctionItemTap(Map<String, dynamic> item) {
    showFeedback("${item['title']} auction clicked!");
  }

  void onCalendarTap() {
    showFeedback("Calendar clicked!");
  }

  void onJoinLiveAuctionTap() {
    showFeedback("Join Live Auction clicked!");
  }

  void showFeedback(String message) {
    Get.snackbar(
      "Ar-Rahnu Auction",
      message,
      backgroundColor: const Color(0xFFFE8000),
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  // Filtered items based on selected category
  List<Map<String, dynamic>> get filteredItems {
    if (selectedCategory.value == 'All') {
      return auctionItems;
    }
    return auctionItems
        .where((item) => item['category'] == selectedCategory.value)
        .toList();
  }
}
