import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NavigationController extends GetxController {
  var currentIndex = 0.obs;

  void changeTabIndex(int index) {
    currentIndex.value = index;

    // Show feedback when switching tabs
    String tabName = ['Home', 'Account'][index];
    Get.snackbar(
      "Navigation",
      "Switched to $tabName",
      backgroundColor: const Color(0xFFFE8000),
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 1),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }
}
