import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'navigation_controller.dart';
import 'account_controller.dart';

class DashboardController extends GetxController {
  // User information
  var userName = "<PERSON>".obs;
  var userEmail = "<EMAIL>".obs;
  var userBalance = "RM 2,500.00".obs;
  var memberSince = "January 2024".obs;

  // Dashboard stats
  var activeBids = 5.obs;
  var watchlistItems = 12.obs;
  var wonAuctions = 3.obs;
  var totalSpent = "RM 15,750.00".obs;

  // Recent activities
  var recentBids = <Map<String, dynamic>>[
    {
      'item': 'Gold Necklace 18K',
      'currentBid': 'RM 850.00',
      'timeLeft': '2h 15m',
      'status': 'leading',
      'image': 'jewelry',
    },
    {
      'item': 'Diamond Ring',
      'currentBid': 'RM 1,200.00',
      'timeLeft': '5h 30m',
      'status': 'outbid',
      'image': 'ring',
    },
    {
      'item': 'Silver Watch',
      'currentBid': 'RM 450.00',
      'timeLeft': '1d 3h',
      'status': 'leading',
      'image': 'watch',
    },
  ].obs;

  // Watchlist items
  var watchlistAuctions = <Map<String, dynamic>>[
    {
      'item': 'Emerald Earrings',
      'currentBid': 'RM 650.00',
      'timeLeft': '3h 45m',
      'image': 'earrings',
    },
    {
      'item': 'Pearl Bracelet',
      'currentBid': 'RM 320.00',
      'timeLeft': '6h 20m',
      'image': 'bracelet',
    },
  ].obs;

  @override
  void onInit() {
    super.onInit();
    loadUserData();
  }

  void loadUserData() {
    // Simulate loading user data
    // In real app, this would fetch from API
    // Don't show feedback during initialization to avoid test issues
  }

  void onProfileTap() {
    showFeedback("Profile page clicked!");
  }

  void onWalletTap() {
    showFeedback("Wallet page clicked!");
  }

  void onBidsHistoryTap() {
    showFeedback("Bids history clicked!");
  }

  void onWatchlistTap() {
    showFeedback("Watchlist clicked!");
  }

  void onSettingsTap() {
    showFeedback("Settings clicked!");
  }

  void onLogoutTap() {
    Get.dialog(
      AlertDialog(
        title: const Text("Logout"),
        content: const Text("Are you sure you want to logout?"),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text("Cancel")),
          TextButton(
            onPressed: () {
              Get.back();
              // Navigate back to main navigation (Account tab)
              Get.find<NavigationController>().changeTabIndex(1);
              Get.find<AccountController>().showLoginPage();
              showFeedback("Logged out successfully!");
            },
            child: const Text("Logout", style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void onBidItemTap(Map<String, dynamic> item) {
    showFeedback("Viewing ${item['item']}");
  }

  void onWatchlistItemTap(Map<String, dynamic> item) {
    showFeedback("Viewing ${item['item']}");
  }

  void showFeedback(String message) {
    Get.snackbar(
      "Dashboard",
      message,
      backgroundColor: const Color(0xFFFE8000),
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }
}
