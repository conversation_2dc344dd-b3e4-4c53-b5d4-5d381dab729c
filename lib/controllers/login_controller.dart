import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'account_controller.dart';
import '../views/dashboard_view.dart';

class LoginController extends GetxController {
  // Text editing controllers
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  // Observable variables
  var isPasswordVisible = false.obs;
  var rememberMe = false.obs;
  var isLoading = false.obs;

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  // Methods
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  void toggleRememberMe(bool? value) {
    rememberMe.value = value ?? false;
  }

  void onForgotPasswordTap() {
    showFeedback("Forgot Password clicked!");
  }

  void onSignUpTap() {
    // Navigate to register page
    final accountController = Get.find<AccountController>();
    accountController.showRegisterPage();
    showFeedback("Switched to Register!");
  }

  Future<void> handleLogin() async {
    if (emailController.text.isEmpty || passwordController.text.isEmpty) {
      showFeedback("Please fill all fields!");
      return;
    }

    isLoading.value = true;

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    isLoading.value = false;

    // Navigate to dashboard
    Get.to(() => const DashboardView());
    showFeedback("Login successful! Welcome to Ar-Rahnu Auction!");
  }

  void showFeedback(String message) {
    Get.snackbar(
      "Ar-Rahnu Auction",
      message,
      backgroundColor: const Color(0xFFFE8000),
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }
}
